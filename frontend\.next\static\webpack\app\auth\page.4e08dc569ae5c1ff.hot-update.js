"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/page",{

/***/ "(app-pages-browser)/./src/components/auth/LoginForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/LoginForm.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginForm: () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ LoginForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Icons for enhanced UI\nconst EmailIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n            lineNumber: 18,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 17,\n        columnNumber: 3\n    }, undefined);\n_c = EmailIcon;\nconst PasswordIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n            lineNumber: 29,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 28,\n        columnNumber: 3\n    }, undefined);\n_c1 = PasswordIcon;\nconst ArrowRightIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n            lineNumber: 40,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\n_c2 = ArrowRightIcon;\nconst LoginForm = (param)=>{\n    let { onSuccess, onSwitchToSignup } = param;\n    _s();\n    const { login } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        password: ''\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.email) {\n            newErrors.email = 'Email is required';\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = 'Please enter a valid email address';\n        }\n        if (!formData.password) {\n            newErrors.password = 'Password is required';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setLoading(true);\n        setErrors({});\n        try {\n            await login(formData.email, formData.password);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        } catch (error) {\n            setErrors({\n                general: error instanceof Error ? error.message : 'Login failed'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleInputChange = (field)=>(e)=>{\n            setFormData((prev)=>({\n                    ...prev,\n                    [field]: e.target.value\n                }));\n            if (errors[field]) {\n                setErrors((prev)=>({\n                        ...prev,\n                        [field]: undefined\n                    }));\n            }\n        };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.AuthCard, {\n        title: \"Welcome Back\",\n        subtitle: \"Sign in to your premium real estate account\",\n        className: \"w-full max-w-md mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [\n                    errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-destructive/10 border border-destructive/20 rounded-xl animate-slide-in-left\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-destructive\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-destructive font-medium\",\n                                    children: errors.general\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Email Address\",\n                                type: \"email\",\n                                value: formData.email,\n                                onChange: handleInputChange('email'),\n                                placeholder: \"Enter your email address\",\n                                autoComplete: \"off\",\n                                error: errors.email,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmailIcon, {}, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 19\n                                }, void 0),\n                                variant: \"floating\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Password\",\n                                type: \"password\",\n                                value: formData.password,\n                                onChange: handleInputChange('password'),\n                                placeholder: \"Enter your password\",\n                                error: errors.password,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PasswordIcon, {}, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 19\n                                }, void 0),\n                                variant: \"floating\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors\",\n                            children: \"Forgot your password?\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        type: \"submit\",\n                        className: \"w-full\",\n                        loading: loading,\n                        size: \"lg\",\n                        variant: \"gradient\",\n                        icon: !loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowRightIcon, {}, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 28\n                        }, void 0) : undefined,\n                        iconPosition: \"right\",\n                        children: loading ? 'Signing In...' : 'Sign In'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative my-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full border-t border-slate-300\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex justify-center text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"px-4 bg-white text-slate-600 font-medium\",\n                            children: \"or\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    type: \"button\",\n                    variant: \"outline\",\n                    className: \"w-full border-slate-300 hover:border-blue-600\",\n                    size: \"lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5 mr-2\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fill: \"#4285F4\",\n                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fill: \"#34A853\",\n                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fill: \"#FBBC05\",\n                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fill: \"#EA4335\",\n                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined),\n                        \"Continue with Google\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-slate-700\",\n                    children: [\n                        \"Don't have an account?\",\n                        ' ',\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onSwitchToSignup,\n                            className: \"text-blue-600 hover:text-blue-700 font-semibold transition-colors\",\n                            children: \"Create your account\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LoginForm, \"hIkPsJa2IBAqTAcnQJd8fCbBLSI=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c3 = LoginForm;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"EmailIcon\");\n$RefreshReg$(_c1, \"PasswordIcon\");\n$RefreshReg$(_c2, \"ArrowRightIcon\");\n$RefreshReg$(_c3, \"LoginForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2F1dGgvTG9naW5Gb3JtLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRXVDO0FBQ1E7QUFDRjtBQUNFO0FBQ0M7QUFRaEQsd0JBQXdCO0FBQ3hCLE1BQU1NLFlBQVksa0JBQ2hCLDhEQUFDQztRQUFJQyxXQUFVO1FBQVVDLE1BQUs7UUFBT0MsUUFBTztRQUFlQyxTQUFRO2tCQUNqRSw0RUFBQ0M7WUFDQ0MsZUFBYztZQUNkQyxnQkFBZTtZQUNmQyxhQUFhO1lBQ2JDLEdBQUU7Ozs7Ozs7Ozs7O0tBTkZWO0FBV04sTUFBTVcsZUFBZSxrQkFDbkIsOERBQUNWO1FBQUlDLFdBQVU7UUFBVUMsTUFBSztRQUFPQyxRQUFPO1FBQWVDLFNBQVE7a0JBQ2pFLDRFQUFDQztZQUNDQyxlQUFjO1lBQ2RDLGdCQUFlO1lBQ2ZDLGFBQWE7WUFDYkMsR0FBRTs7Ozs7Ozs7Ozs7TUFORkM7QUFXTixNQUFNQyxpQkFBaUIsa0JBQ3JCLDhEQUFDWDtRQUFJQyxXQUFVO1FBQVVDLE1BQUs7UUFBT0MsUUFBTztRQUFlQyxTQUFRO2tCQUNqRSw0RUFBQ0M7WUFBS0MsZUFBYztZQUFRQyxnQkFBZTtZQUFRQyxhQUFhO1lBQUdDLEdBQUU7Ozs7Ozs7Ozs7O01BRm5FRTtBQU1DLE1BQU1DLFlBQXNDO1FBQUMsRUFBRUMsU0FBUyxFQUFFQyxnQkFBZ0IsRUFBRTs7SUFDakYsTUFBTSxFQUFFQyxLQUFLLEVBQUUsR0FBR2pCLDhEQUFPQTtJQUN6QixNQUFNLENBQUNrQixVQUFVQyxZQUFZLEdBQUd2QiwrQ0FBUUEsQ0FBWTtRQUNsRHdCLE9BQU87UUFDUEMsVUFBVTtJQUNaO0lBQ0EsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUczQiwrQ0FBUUEsQ0FBMkMsQ0FBQztJQUNoRixNQUFNLENBQUM0QixTQUFTQyxXQUFXLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUV2QyxNQUFNOEIsZUFBZTtRQUNuQixNQUFNQyxZQUEyQixDQUFDO1FBRWxDLElBQUksQ0FBQ1QsU0FBU0UsS0FBSyxFQUFFO1lBQ25CTyxVQUFVUCxLQUFLLEdBQUc7UUFDcEIsT0FBTyxJQUFJLENBQUMsZUFBZVEsSUFBSSxDQUFDVixTQUFTRSxLQUFLLEdBQUc7WUFDL0NPLFVBQVVQLEtBQUssR0FBRztRQUNwQjtRQUVBLElBQUksQ0FBQ0YsU0FBU0csUUFBUSxFQUFFO1lBQ3RCTSxVQUFVTixRQUFRLEdBQUc7UUFDdkI7UUFFQUUsVUFBVUk7UUFDVixPQUFPRSxPQUFPQyxJQUFJLENBQUNILFdBQVdJLE1BQU0sS0FBSztJQUMzQztJQUVBLE1BQU1DLGVBQWUsT0FBT0M7UUFDMUJBLEVBQUVDLGNBQWM7UUFFaEIsSUFBSSxDQUFDUixnQkFBZ0I7UUFFckJELFdBQVc7UUFDWEYsVUFBVSxDQUFDO1FBRVgsSUFBSTtZQUNGLE1BQU1OLE1BQU1DLFNBQVNFLEtBQUssRUFBRUYsU0FBU0csUUFBUTtZQUM3Q04sc0JBQUFBLGdDQUFBQTtRQUNGLEVBQUUsT0FBT29CLE9BQU87WUFDZFosVUFBVTtnQkFBRWEsU0FBU0QsaUJBQWlCRSxRQUFRRixNQUFNRyxPQUFPLEdBQUc7WUFBZTtRQUMvRSxTQUFVO1lBQ1JiLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTWMsb0JBQW9CLENBQUNDLFFBQTJCLENBQUNQO1lBQ3JEZCxZQUFZc0IsQ0FBQUEsT0FBUztvQkFBRSxHQUFHQSxJQUFJO29CQUFFLENBQUNELE1BQU0sRUFBRVAsRUFBRVMsTUFBTSxDQUFDQyxLQUFLO2dCQUFDO1lBQ3hELElBQUlyQixNQUFNLENBQUNrQixNQUFNLEVBQUU7Z0JBQ2pCakIsVUFBVWtCLENBQUFBLE9BQVM7d0JBQUUsR0FBR0EsSUFBSTt3QkFBRSxDQUFDRCxNQUFNLEVBQUVJO29CQUFVO1lBQ25EO1FBQ0Y7SUFFQSxxQkFDRSw4REFBQzdDLHlEQUFRQTtRQUNQOEMsT0FBTTtRQUNOQyxVQUFTO1FBQ1QzQyxXQUFVOzswQkFFViw4REFBQzRDO2dCQUFLQyxVQUFVaEI7Z0JBQWM3QixXQUFVOztvQkFDckNtQixPQUFPYyxPQUFPLGtCQUNiLDhEQUFDYTt3QkFBSTlDLFdBQVU7a0NBQ2IsNEVBQUM4Qzs0QkFBSTlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTtvQ0FBMkJDLE1BQUs7b0NBQWVFLFNBQVE7OENBQ3BFLDRFQUFDQzt3Q0FDQzJDLFVBQVM7d0NBQ1R2QyxHQUFFO3dDQUNGd0MsVUFBUzs7Ozs7Ozs7Ozs7OENBR2IsOERBQUNDO29DQUFFakQsV0FBVTs4Q0FBd0NtQixPQUFPYyxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLekUsOERBQUNhO3dCQUFJOUMsV0FBVTs7MENBQ2IsOERBQUNMLHVEQUFLQTtnQ0FDSnVELE9BQU07Z0NBQ05DLE1BQUs7Z0NBQ0xYLE9BQU96QixTQUFTRSxLQUFLO2dDQUNyQm1DLFVBQVVoQixrQkFBa0I7Z0NBQzVCaUIsYUFBWTtnQ0FDWkMsY0FBYTtnQ0FDYnRCLE9BQU9iLE9BQU9GLEtBQUs7Z0NBQ25Cc0Msb0JBQU0sOERBQUN6RDs7Ozs7Z0NBQ1AwRCxTQUFRO2dDQUNSQyxRQUFROzs7Ozs7MENBR1YsOERBQUM5RCx1REFBS0E7Z0NBQ0p1RCxPQUFNO2dDQUNOQyxNQUFLO2dDQUNMWCxPQUFPekIsU0FBU0csUUFBUTtnQ0FDeEJrQyxVQUFVaEIsa0JBQWtCO2dDQUM1QmlCLGFBQVk7Z0NBQ1pyQixPQUFPYixPQUFPRCxRQUFRO2dDQUN0QnFDLG9CQUFNLDhEQUFDOUM7Ozs7O2dDQUNQK0MsU0FBUTtnQ0FDUkMsUUFBUTs7Ozs7Ozs7Ozs7O2tDQUtaLDhEQUFDWDt3QkFBSTlDLFdBQVU7a0NBQ2IsNEVBQUMwRDs0QkFBT1AsTUFBSzs0QkFBU25ELFdBQVU7c0NBQTBFOzs7Ozs7Ozs7OztrQ0FLNUcsOERBQUNOLHlEQUFNQTt3QkFDTHlELE1BQUs7d0JBQ0xuRCxXQUFVO3dCQUNWcUIsU0FBU0E7d0JBQ1RzQyxNQUFLO3dCQUNMSCxTQUFRO3dCQUNSRCxNQUFNLENBQUNsQyx3QkFBVSw4REFBQ1g7Ozs7cUNBQW9CK0I7d0JBQ3RDbUIsY0FBYTtrQ0FFWnZDLFVBQVUsa0JBQWtCOzs7Ozs7Ozs7Ozs7MEJBS2pDLDhEQUFDeUI7Z0JBQUk5QyxXQUFVOztrQ0FDYiw4REFBQzhDO3dCQUFJOUMsV0FBVTtrQ0FDYiw0RUFBQzhDOzRCQUFJOUMsV0FBVTs7Ozs7Ozs7Ozs7a0NBRWpCLDhEQUFDOEM7d0JBQUk5QyxXQUFVO2tDQUNiLDRFQUFDNkQ7NEJBQUs3RCxXQUFVO3NDQUEyQzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSy9ELDhEQUFDOEM7Z0JBQUk5QyxXQUFVOzBCQUNiLDRFQUFDTix5REFBTUE7b0JBQUN5RCxNQUFLO29CQUFTSyxTQUFRO29CQUFVeEQsV0FBVTtvQkFBZ0QyRCxNQUFLOztzQ0FDckcsOERBQUM1RDs0QkFBSUMsV0FBVTs0QkFBZUcsU0FBUTs7OENBQ3BDLDhEQUFDQztvQ0FDQ0gsTUFBSztvQ0FDTE8sR0FBRTs7Ozs7OzhDQUVKLDhEQUFDSjtvQ0FDQ0gsTUFBSztvQ0FDTE8sR0FBRTs7Ozs7OzhDQUVKLDhEQUFDSjtvQ0FDQ0gsTUFBSztvQ0FDTE8sR0FBRTs7Ozs7OzhDQUVKLDhEQUFDSjtvQ0FDQ0gsTUFBSztvQ0FDTE8sR0FBRTs7Ozs7Ozs7Ozs7O3dCQUVBOzs7Ozs7Ozs7Ozs7MEJBTVYsOERBQUNzQztnQkFBSTlDLFdBQVU7MEJBQ2IsNEVBQUNpRDtvQkFBRWpELFdBQVU7O3dCQUF5Qjt3QkFDUjtzQ0FDNUIsOERBQUMwRDs0QkFDQ1AsTUFBSzs0QkFDTFcsU0FBU2pEOzRCQUNUYixXQUFVO3NDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9YLEVBQUM7R0ExS1lXOztRQUNPZCwwREFBT0E7OztNQURkYyIsInNvdXJjZXMiOlsiRTpcXENvZGVcXFBvcnRmb2xpb1xcTmV3TVJIXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxhdXRoXFxMb2dpbkZvcm0udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9CdXR0b24nXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9JbnB1dCdcbmltcG9ydCB7IEF1dGhDYXJkIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0NhcmQnXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcbmltcG9ydCB7IExvZ2luRGF0YSB9IGZyb20gJ0AvdHlwZXMvYXV0aCdcblxuaW50ZXJmYWNlIExvZ2luRm9ybVByb3BzIHtcbiAgb25TdWNjZXNzPzogKCkgPT4gdm9pZFxuICBvblN3aXRjaFRvU2lnbnVwPzogKCkgPT4gdm9pZFxufVxuXG4vLyBJY29ucyBmb3IgZW5oYW5jZWQgVUlcbmNvbnN0IEVtYWlsSWNvbiA9ICgpID0+IChcbiAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgPHBhdGhcbiAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgZD1cIk0xNiAxMmE0IDQgMCAxMC04IDAgNCA0IDAgMDA4IDB6bTAgMHYxLjVhMi41IDIuNSAwIDAwNSAwVjEyYTkgOSAwIDEwLTkgOW00LjUtMS4yMDZhOC45NTkgOC45NTkgMCAwMS00LjUgMS4yMDdcIlxuICAgIC8+XG4gIDwvc3ZnPlxuKVxuXG5jb25zdCBQYXNzd29yZEljb24gPSAoKSA9PiAoXG4gIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgIDxwYXRoXG4gICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgIGQ9XCJNMTIgMTV2Mm0tNiA0aDEyYTIgMiAwIDAwMi0ydi02YTIgMiAwIDAwLTItMkg2YTIgMiAwIDAwLTIgMnY2YTIgMiAwIDAwMiAyem0xMC0xMFY3YTQgNCAwIDAwLTggMHY0aDh6XCJcbiAgICAvPlxuICA8L3N2Zz5cbilcblxuY29uc3QgQXJyb3dSaWdodEljb24gPSAoKSA9PiAoXG4gIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xNyA4bDQgNG0wIDBsLTQgNG00LTRIM1wiIC8+XG4gIDwvc3ZnPlxuKVxuXG5leHBvcnQgY29uc3QgTG9naW5Gb3JtOiBSZWFjdC5GQzxMb2dpbkZvcm1Qcm9wcz4gPSAoeyBvblN1Y2Nlc3MsIG9uU3dpdGNoVG9TaWdudXAgfSkgPT4ge1xuICBjb25zdCB7IGxvZ2luIH0gPSB1c2VBdXRoKClcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZTxMb2dpbkRhdGE+KHtcbiAgICBlbWFpbDogJycsXG4gICAgcGFzc3dvcmQ6ICcnXG4gIH0pXG4gIGNvbnN0IFtlcnJvcnMsIHNldEVycm9yc10gPSB1c2VTdGF0ZTxQYXJ0aWFsPExvZ2luRGF0YSAmIHsgZ2VuZXJhbDogc3RyaW5nIH0+Pih7fSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgY29uc3QgdmFsaWRhdGVGb3JtID0gKCk6IGJvb2xlYW4gPT4ge1xuICAgIGNvbnN0IG5ld0Vycm9yczogdHlwZW9mIGVycm9ycyA9IHt9XG5cbiAgICBpZiAoIWZvcm1EYXRhLmVtYWlsKSB7XG4gICAgICBuZXdFcnJvcnMuZW1haWwgPSAnRW1haWwgaXMgcmVxdWlyZWQnXG4gICAgfSBlbHNlIGlmICghL1xcUytAXFxTK1xcLlxcUysvLnRlc3QoZm9ybURhdGEuZW1haWwpKSB7XG4gICAgICBuZXdFcnJvcnMuZW1haWwgPSAnUGxlYXNlIGVudGVyIGEgdmFsaWQgZW1haWwgYWRkcmVzcydcbiAgICB9XG5cbiAgICBpZiAoIWZvcm1EYXRhLnBhc3N3b3JkKSB7XG4gICAgICBuZXdFcnJvcnMucGFzc3dvcmQgPSAnUGFzc3dvcmQgaXMgcmVxdWlyZWQnXG4gICAgfVxuXG4gICAgc2V0RXJyb3JzKG5ld0Vycm9ycylcbiAgICByZXR1cm4gT2JqZWN0LmtleXMobmV3RXJyb3JzKS5sZW5ndGggPT09IDBcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KClcblxuICAgIGlmICghdmFsaWRhdGVGb3JtKCkpIHJldHVyblxuXG4gICAgc2V0TG9hZGluZyh0cnVlKVxuICAgIHNldEVycm9ycyh7fSlcblxuICAgIHRyeSB7XG4gICAgICBhd2FpdCBsb2dpbihmb3JtRGF0YS5lbWFpbCwgZm9ybURhdGEucGFzc3dvcmQpXG4gICAgICBvblN1Y2Nlc3M/LigpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldEVycm9ycyh7IGdlbmVyYWw6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0xvZ2luIGZhaWxlZCcgfSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IChmaWVsZDoga2V5b2YgTG9naW5EYXRhKSA9PiAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIFtmaWVsZF06IGUudGFyZ2V0LnZhbHVlIH0pKVxuICAgIGlmIChlcnJvcnNbZmllbGRdKSB7XG4gICAgICBzZXRFcnJvcnMocHJldiA9PiAoeyAuLi5wcmV2LCBbZmllbGRdOiB1bmRlZmluZWQgfSkpXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8QXV0aENhcmRcbiAgICAgIHRpdGxlPVwiV2VsY29tZSBCYWNrXCJcbiAgICAgIHN1YnRpdGxlPVwiU2lnbiBpbiB0byB5b3VyIHByZW1pdW0gcmVhbCBlc3RhdGUgYWNjb3VudFwiXG4gICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctbWQgbXgtYXV0b1wiXG4gICAgPlxuICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIHtlcnJvcnMuZ2VuZXJhbCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYmctZGVzdHJ1Y3RpdmUvMTAgYm9yZGVyIGJvcmRlci1kZXN0cnVjdGl2ZS8yMCByb3VuZGVkLXhsIGFuaW1hdGUtc2xpZGUtaW4tbGVmdFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1kZXN0cnVjdGl2ZVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgIGZpbGxSdWxlPVwiZXZlbm9kZFwiXG4gICAgICAgICAgICAgICAgICBkPVwiTTE4IDEwYTggOCAwIDExLTE2IDAgOCA4IDAgMDExNiAwem0tNyA0YTEgMSAwIDExLTIgMCAxIDEgMCAwMTIgMHptLTEtOWExIDEgMCAwMC0xIDF2NGExIDEgMCAxMDIgMFY2YTEgMSAwIDAwLTEtMXpcIlxuICAgICAgICAgICAgICAgICAgY2xpcFJ1bGU9XCJldmVub2RkXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWRlc3RydWN0aXZlIGZvbnQtbWVkaXVtXCI+e2Vycm9ycy5nZW5lcmFsfTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS01XCI+XG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBsYWJlbD1cIkVtYWlsIEFkZHJlc3NcIlxuICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5lbWFpbH1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZSgnZW1haWwnKX1cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBlbWFpbCBhZGRyZXNzXCJcbiAgICAgICAgICAgIGF1dG9Db21wbGV0ZT1cIm9mZlwiXG4gICAgICAgICAgICBlcnJvcj17ZXJyb3JzLmVtYWlsfVxuICAgICAgICAgICAgaWNvbj17PEVtYWlsSWNvbiAvPn1cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJmbG9hdGluZ1wiXG4gICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgIC8+XG5cbiAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgIGxhYmVsPVwiUGFzc3dvcmRcIlxuICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wYXNzd29yZH1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZSgncGFzc3dvcmQnKX1cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBwYXNzd29yZFwiXG4gICAgICAgICAgICBlcnJvcj17ZXJyb3JzLnBhc3N3b3JkfVxuICAgICAgICAgICAgaWNvbj17PFBhc3N3b3JkSWNvbiAvPn1cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJmbG9hdGluZ1wiXG4gICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBGb3Jnb3QgUGFzc3dvcmQgTGluayAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgPGJ1dHRvbiB0eXBlPVwiYnV0dG9uXCIgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS03MDAgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgIEZvcmdvdCB5b3VyIHBhc3N3b3JkP1xuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxuICAgICAgICAgIHNpemU9XCJsZ1wiXG4gICAgICAgICAgdmFyaWFudD1cImdyYWRpZW50XCJcbiAgICAgICAgICBpY29uPXshbG9hZGluZyA/IDxBcnJvd1JpZ2h0SWNvbiAvPiA6IHVuZGVmaW5lZH1cbiAgICAgICAgICBpY29uUG9zaXRpb249XCJyaWdodFwiXG4gICAgICAgID5cbiAgICAgICAgICB7bG9hZGluZyA/ICdTaWduaW5nIEluLi4uJyA6ICdTaWduIEluJ31cbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L2Zvcm0+XG5cbiAgICAgIHsvKiBEaXZpZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBteS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJvcmRlci10IGJvcmRlci1zbGF0ZS0zMDBcIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgZmxleCBqdXN0aWZ5LWNlbnRlciB0ZXh0LXNtXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtNCBiZy13aGl0ZSB0ZXh0LXNsYXRlLTYwMCBmb250LW1lZGl1bVwiPm9yPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogU29jaWFsIExvZ2luIE9wdGlvbnMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICA8QnV0dG9uIHR5cGU9XCJidXR0b25cIiB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cInctZnVsbCBib3JkZXItc2xhdGUtMzAwIGhvdmVyOmJvcmRlci1ibHVlLTYwMFwiIHNpemU9XCJsZ1wiPlxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNSBtci0yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgZmlsbD1cIiM0Mjg1RjRcIlxuICAgICAgICAgICAgICBkPVwiTTIyLjU2IDEyLjI1YzAtLjc4LS4wNy0xLjUzLS4yLTIuMjVIMTJ2NC4yNmg1LjkyYy0uMjYgMS4zNy0xLjA0IDIuNTMtMi4yMSAzLjMxdjIuNzdoMy41N2MyLjA4LTEuOTIgMy4yOC00Ljc0IDMuMjgtOC4wOXpcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgIGZpbGw9XCIjMzRBODUzXCJcbiAgICAgICAgICAgICAgZD1cIk0xMiAyM2MyLjk3IDAgNS40Ni0uOTggNy4yOC0yLjY2bC0zLjU3LTIuNzdjLS45OC42Ni0yLjIzIDEuMDYtMy43MSAxLjA2LTIuODYgMC01LjI5LTEuOTMtNi4xNi00LjUzSDIuMTh2Mi44NEMzLjk5IDIwLjUzIDcuNyAyMyAxMiAyM3pcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgIGZpbGw9XCIjRkJCQzA1XCJcbiAgICAgICAgICAgICAgZD1cIk01Ljg0IDE0LjA5Yy0uMjItLjY2LS4zNS0xLjM2LS4zNS0yLjA5cy4xMy0xLjQzLjM1LTIuMDlWNy4wN0gyLjE4QzEuNDMgOC41NSAxIDEwLjIyIDEgMTJzLjQzIDMuNDUgMS4xOCA0LjkzbDIuODUtMi4yMi44MS0uNjJ6XCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICBmaWxsPVwiI0VBNDMzNVwiXG4gICAgICAgICAgICAgIGQ9XCJNMTIgNS4zOGMxLjYyIDAgMy4wNi41NiA0LjIxIDEuNjRsMy4xNS0zLjE1QzE3LjQ1IDIuMDkgMTQuOTcgMSAxMiAxIDcuNyAxIDMuOTkgMy40NyAyLjE4IDcuMDdsMy42NiAyLjg0Yy44Ny0yLjYgMy4zLTQuNTMgNi4xNi00LjUzelwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIENvbnRpbnVlIHdpdGggR29vZ2xlXG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBTd2l0Y2ggdG8gU2lnbnVwICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04IHRleHQtY2VudGVyXCI+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zbGF0ZS03MDBcIj5cbiAgICAgICAgICBEb24mYXBvczt0IGhhdmUgYW4gYWNjb3VudD97JyAnfVxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgb25DbGljaz17b25Td2l0Y2hUb1NpZ251cH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTcwMCBmb250LXNlbWlib2xkIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBDcmVhdGUgeW91ciBhY2NvdW50XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuICAgIDwvQXV0aENhcmQ+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiQnV0dG9uIiwiSW5wdXQiLCJBdXRoQ2FyZCIsInVzZUF1dGgiLCJFbWFpbEljb24iLCJzdmciLCJjbGFzc05hbWUiLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJQYXNzd29yZEljb24iLCJBcnJvd1JpZ2h0SWNvbiIsIkxvZ2luRm9ybSIsIm9uU3VjY2VzcyIsIm9uU3dpdGNoVG9TaWdudXAiLCJsb2dpbiIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJlbWFpbCIsInBhc3N3b3JkIiwiZXJyb3JzIiwic2V0RXJyb3JzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJ2YWxpZGF0ZUZvcm0iLCJuZXdFcnJvcnMiLCJ0ZXN0IiwiT2JqZWN0Iiwia2V5cyIsImxlbmd0aCIsImhhbmRsZVN1Ym1pdCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsImVycm9yIiwiZ2VuZXJhbCIsIkVycm9yIiwibWVzc2FnZSIsImhhbmRsZUlucHV0Q2hhbmdlIiwiZmllbGQiLCJwcmV2IiwidGFyZ2V0IiwidmFsdWUiLCJ1bmRlZmluZWQiLCJ0aXRsZSIsInN1YnRpdGxlIiwiZm9ybSIsIm9uU3VibWl0IiwiZGl2IiwiZmlsbFJ1bGUiLCJjbGlwUnVsZSIsInAiLCJsYWJlbCIsInR5cGUiLCJvbkNoYW5nZSIsInBsYWNlaG9sZGVyIiwiYXV0b0NvbXBsZXRlIiwiaWNvbiIsInZhcmlhbnQiLCJyZXF1aXJlZCIsImJ1dHRvbiIsInNpemUiLCJpY29uUG9zaXRpb24iLCJzcGFuIiwib25DbGljayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/LoginForm.tsx\n"));

/***/ })

});