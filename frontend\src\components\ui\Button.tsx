import React from 'react'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'gradient' | 'premium'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  children: React.ReactNode
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  icon,
  iconPosition = 'left',
  children,
  className = '',
  disabled,
  ...props
}) => {
  const baseClasses = `
    inline-flex items-center justify-center font-semibold
    transition-all duration-300 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    transform hover:scale-105 active:scale-95
    relative overflow-hidden
  `.trim()

  const variantClasses = {
    primary: `
      bg-blue-600 text-white
      hover:bg-blue-700 hover:shadow-lg
      focus:ring-blue-500 focus:ring-2 focus:ring-offset-2
      shadow-md
    `.trim(),
    secondary: `
      bg-slate-600 text-white
      hover:bg-slate-700 hover:shadow-lg
      focus:ring-slate-500 focus:ring-2 focus:ring-offset-2
      shadow-md
    `.trim(),
    outline: `
      border-2 border-blue-600 bg-transparent text-blue-600
      hover:bg-blue-600 hover:text-white hover:shadow-lg
      focus:ring-blue-500 focus:ring-2 focus:ring-offset-2
    `.trim(),
    ghost: `
      bg-transparent text-blue-600
      hover:bg-blue-50 hover:shadow-md
      focus:ring-blue-500 focus:ring-2 focus:ring-offset-2
    `.trim(),
    gradient: `
      bg-gradient-to-r from-blue-600 to-blue-700
      text-white shadow-lg
      hover:shadow-xl hover:from-blue-700 hover:to-blue-800
      focus:ring-blue-500 focus:ring-2 focus:ring-offset-2
      before:absolute before:inset-0 before:bg-gradient-to-r
      before:from-white/20 before:to-transparent before:opacity-0
      hover:before:opacity-100 before:transition-opacity before:duration-300
    `.trim(),
    premium: `
      bg-gradient-to-r from-slate-800 to-blue-900
      text-white shadow-xl
      hover:shadow-2xl hover:from-blue-900 hover:to-slate-800
      focus:ring-blue-500 focus:ring-2 focus:ring-offset-2
      before:absolute before:inset-0 before:bg-gradient-to-r
      before:from-white/30 before:to-transparent before:opacity-0
      hover:before:opacity-100 before:transition-opacity before:duration-300
    `.trim()
  }

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm rounded-lg',
    md: 'px-6 py-3 text-sm rounded-xl',
    lg: 'px-8 py-4 text-base rounded-xl',
    xl: 'px-10 py-5 text-lg rounded-2xl'
  }

  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`

  const LoadingSpinner = () => (
    <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  )

  return (
    <button className={classes} disabled={disabled || loading} {...props}>
      {loading ? (
        <>
          <LoadingSpinner />
          <span className="ml-2">Loading...</span>
        </>
      ) : (
        <>
          {icon && iconPosition === 'left' && <span className="mr-2 flex items-center">{icon}</span>}
          <span className="relative z-10">{children}</span>
          {icon && iconPosition === 'right' && <span className="ml-2 flex items-center">{icon}</span>}
        </>
      )}
    </button>
  )
}
